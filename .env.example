# =============================================================================
# LLM配置 - 只使用DeepSeek (简化配置)
# =============================================================================
# 默认LLM配置 - DeepSeek (通用任务)
LLM_PROVIDER=deepseek
LLM_API_KEY=***********************************
LLM_BASE_URL=https://api.deepseek.com
LLM_MODEL=deepseek-chat
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=8000
LLM_TIMEOUT=60
LLM_MAX_RETRIES=3

# =============================================================================
# 数据库配置
# =============================================================================
# PostgreSQL数据库连接（请根据实际情况修改）
# 注意：密码中的@符号需要URL编码为%40
DATABASE_URL=postgresql://zhaoyingnan:zhaoyingnan%40119@*************:18640/abument_firefighting_supervision

# 数据库连接详细配置
DB_HOST=*************
DB_PORT=18640
DB_USER=zhaoyingnan
DB_PASSWORD=zhaoyingnan@119
DB_DEFAULT_DATABASE=abument_firefighting_supervision
DB_DEFAULT_SCHEMA=public

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false

# 多数据库支持配置（后续扩展）
# 格式：DB_DATABASES=database1,database2,database3
DB_DATABASES=abument_firefighting_supervision,integrated_service_governance
DB_SCHEMAS=public,information_schema

# LangSmith Configuration (optional)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=sql-agent-project
