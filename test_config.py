#!/usr/bin/env python3
"""
测试配置脚本 - 验证DeepSeek API和PostgreSQL连接
"""

import os
import sys
from dotenv import load_dotenv

def test_environment():
    """测试环境变量配置"""
    print("🔍 检查环境变量配置...")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查DeepSeek配置
    llm_api_key = os.getenv("LLM_API_KEY")
    llm_base_url = os.getenv("LLM_BASE_URL")
    llm_model = os.getenv("LLM_MODEL")
    
    print(f"✅ LLM API Key: {'已配置' if llm_api_key else '❌ 未配置'}")
    print(f"✅ LLM Base URL: {llm_base_url or '❌ 未配置'}")
    print(f"✅ LLM Model: {llm_model or '❌ 未配置'}")
    
    # 检查数据库配置
    database_url = os.getenv("DATABASE_URL")
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_user = os.getenv("DB_USER")
    
    print(f"✅ Database URL: {'已配置' if database_url else '❌ 未配置'}")
    print(f"✅ DB Host: {db_host or '❌ 未配置'}")
    print(f"✅ DB Port: {db_port or '❌ 未配置'}")
    print(f"✅ DB User: {db_user or '❌ 未配置'}")
    
    return all([llm_api_key, llm_base_url, llm_model, database_url])

def test_deepseek_api():
    """测试DeepSeek API连接"""
    print("\n🔍 测试DeepSeek API连接...")
    
    try:
        import requests
        
        api_key = os.getenv("LLM_API_KEY")
        base_url = os.getenv("LLM_BASE_URL", "https://api.deepseek.com")
        
        if not api_key:
            print("❌ API密钥未配置")
            return False
        
        # 测试API连接
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{base_url}/v1/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ DeepSeek API连接成功")
            models = response.json().get("data", [])
            print(f"✅ 可用模型数量: {len(models)}")
            return True
        else:
            print(f"❌ API连接失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试异常: {str(e)}")
        return False

def test_database_connection():
    """测试PostgreSQL数据库连接"""
    print("\n🔍 测试PostgreSQL数据库连接...")
    
    try:
        import psycopg2
        from urllib.parse import urlparse
        
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("❌ 数据库URL未配置")
            return False
        
        # 解析数据库URL
        parsed = urlparse(database_url)
        
        # 尝试连接数据库
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port,
            user=parsed.username,
            password=parsed.password,
            database=parsed.path[1:]  # 移除开头的 '/'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        
        cursor.execute("SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
        table_count = cursor.fetchone()[0]
        
        print("✅ PostgreSQL连接成功")
        print(f"✅ 数据库版本: {version}")
        print(f"✅ 公共表数量: {table_count}")
        
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        print("❌ psycopg2未安装，请运行: pip install psycopg2-binary")
        return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 SQL Agent 配置测试")
    print("=" * 50)
    
    # 测试环境变量
    env_ok = test_environment()
    
    if not env_ok:
        print("\n❌ 环境变量配置不完整，请检查.env文件")
        sys.exit(1)
    
    # 测试API连接
    api_ok = test_deepseek_api()
    
    # 测试数据库连接
    db_ok = test_database_connection()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"环境变量: {'✅ 通过' if env_ok else '❌ 失败'}")
    print(f"DeepSeek API: {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"PostgreSQL: {'✅ 通过' if db_ok else '❌ 失败'}")
    
    if all([env_ok, api_ok, db_ok]):
        print("\n🎉 所有测试通过！可以启动SQL Agent了。")
        print("运行命令: langgraph dev")
    else:
        print("\n⚠️  部分测试失败，请检查配置后重试。")
        sys.exit(1)

if __name__ == "__main__":
    main()
