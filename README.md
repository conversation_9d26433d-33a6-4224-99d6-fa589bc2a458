# SQL Agent with Generative UI

A LangGraph-based SQL agent that can answer questions about SQL databases and generate rich UI components to display results. Compatible with DeepSeek API and other OpenAI-compatible LLM providers.

## Features

- 🗄️ **SQL Database Integration**: Connect to SQLite, PostgreSQL, MySQL, and other databases
- 🤖 **Intelligent Query Generation**: Uses LLM to generate SQL queries from natural language questions
- 🎨 **Generative UI**: Rich UI components that display query results, schemas, and errors
- 🔧 **DeepSeek API Support**: Optimized for DeepSeek API with fallback to OpenAI
- 📊 **Interactive Results**: Tables, charts, and formatted data display
- 🛡️ **Query Validation**: Built-in query checking and error handling

## Quick Start

### 🚀 自动安装 (推荐)

```bash
# 克隆或下载项目后，运行自动安装脚本
./setup.sh

# 启动服务
./start.sh
```

### 📋 手动安装

#### 1. 环境配置

```bash
cp .env.example .env
nano .env  # 编辑配置文件
```

配置您的DeepSeek API和PostgreSQL凭据：

```env
# DeepSeek API Configuration
LLM_PROVIDER=deepseek
LLM_API_KEY=***********************************
LLM_BASE_URL=https://api.deepseek.com
LLM_MODEL=deepseek-chat

# PostgreSQL Database Configuration
DATABASE_URL=postgresql://zhaoyingnan:zhaoyingnan%40119@*************:18640/abument_firefighting_supervision
```

#### 2. 安装依赖

```bash
pip install -e . "langgraph-cli[inmem]"
```

#### 3. 测试配置

```bash
python3 test_config.py
```

#### 4. 启动服务

```bash
langgraph dev
```

服务将在 `http://localhost:2024` 启动，并自动打开LangGraph Studio。

## Usage Examples

Ask natural language questions about your database in Chinese or English:

**中文查询示例:**
- "显示所有消防设施的数量"
- "查询最近一个月的火灾报警记录"
- "统计各区域的消防检查情况"
- "显示消防设备维护记录"

**English Query Examples:**
- "Show all fire safety equipment"
- "List recent fire alarm records"
- "Count fire inspections by region"
- "Display equipment maintenance logs"

The agent will:
1. 📋 List available database tables
2. 🔍 Analyze relevant table schemas
3. ⚡ Generate appropriate SQL queries
4. ✅ Execute queries safely
5. 📊 Display results in rich UI components
6. 💡 Provide natural language answers

## Architecture

The SQL agent follows a structured workflow:

```
User Question → List Tables → Get Schema → Generate Query → Execute Query → Format Response
```

Each step generates UI components to show the process and results.

## DeepSeek API Integration

This agent is optimized for DeepSeek API but supports any OpenAI-compatible provider:

```python
# DeepSeek configuration
llm = init_chat_model(
    model="openai:deepseek-chat",
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    base_url="https://api.deepseek.com/v1"
)
```

## UI Components

The agent includes several UI components:

- **TableListComponent**: Shows available database tables
- **SchemaDisplayComponent**: Displays table schemas
- **QueryDisplayComponent**: Shows generated SQL queries
- **QueryResultsComponent**: Interactive results tables
- **QueryErrorComponent**: Error messages and debugging info
- **FinalResponseComponent**: Natural language answers

## Database Support

Currently configured for:

- **PostgreSQL** (Primary): 消防监管数据库 (abument_firefighting_supervision)
- **SQLite** (Fallback): Sample Chinook database for testing

The agent is optimized for PostgreSQL and includes Chinese language support for firefighting supervision data.

## Security Notes

⚠️ **Important**: This agent executes model-generated SQL queries. Always:

- Use read-only database connections
- Limit database permissions
- Validate queries before execution
- Monitor for suspicious activity

## Development

### Project Structure

```
src/agent/
├── graph.py          # Main agent logic
├── ui.tsx           # React UI components
└── styles.css       # Component styling
```

### Adding New Features

1. Extend the `State` TypedDict for new data
2. Add new nodes to the graph
3. Create corresponding UI components
4. Update the workflow edges

<!--
Configuration auto-generated by `langgraph template lock`. DO NOT EDIT MANUALLY.
{
  "config_schemas": {
    "agent": {
      "type": "object",
      "properties": {}
    }
  }
}
-->
