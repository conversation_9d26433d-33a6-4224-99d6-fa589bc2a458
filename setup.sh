#!/bin/bash

# SQL Agent 安装脚本
# 自动安装依赖并配置环境

set -e

echo "🚀 SQL Agent 安装脚本"
echo "======================="

# 检查Python版本
echo "🔍 检查Python版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}')
echo "✅ Python版本: $python_version"

# 检查pip
echo "🔍 检查pip..."
if ! command -v pip &> /dev/null; then
    echo "❌ pip未找到，请先安装pip"
    exit 1
fi
echo "✅ pip已安装"

# 升级pip
echo "📦 升级pip..."
pip install --upgrade pip

# 安装项目依赖
echo "📦 安装项目依赖..."
pip install -e .

# 安装LangGraph CLI
echo "📦 安装LangGraph CLI..."
pip install "langgraph-cli[inmem]"

# 检查.env文件
echo "🔍 检查环境配置..."
if [ ! -f ".env" ]; then
    echo "📝 创建.env文件..."
    cp .env.example .env
    echo "⚠️  请编辑.env文件，配置您的API密钥和数据库连接信息"
    echo "   编辑命令: nano .env"
else
    echo "✅ .env文件已存在"
fi

# 测试配置
echo "🧪 测试配置..."
if python3 test_config.py; then
    echo ""
    echo "🎉 安装完成！"
    echo "==================="
    echo ""
    echo "📋 下一步操作:"
    echo "1. 确保.env文件中的配置正确"
    echo "2. 运行测试: python3 test_config.py"
    echo "3. 启动服务: langgraph dev"
    echo ""
    echo "🌐 服务将在 http://localhost:2024 启动"
    echo ""
    echo "📚 使用说明请查看: USAGE.md"
else
    echo ""
    echo "⚠️  配置测试失败，请检查.env文件中的配置"
    echo "   编辑配置: nano .env"
    echo "   重新测试: python3 test_config.py"
fi

echo ""
echo "🔧 故障排除:"
echo "- 如果遇到依赖问题，请运行: pip install --upgrade -e ."
echo "- 如果数据库连接失败，请检查网络和凭据"
echo "- 如果API调用失败，请验证DeepSeek API密钥"
echo ""
echo "📞 需要帮助？请查看README.md和USAGE.md文档"
