"""SQL Agent with UI support using LangGraph.

This agent can answer questions about SQL databases and generate UI components
to display results. Compatible with DeepSeek API.
"""

from __future__ import annotations

import os
import uuid
from typing import Annotated, Any, Dict, Literal, Sequence, TypedDict

import requests
from langchain_openai import ChatOpenAI
from langchain_community.agent_toolkits import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.graph.ui import AnyUIMessage, push_ui_message, ui_message_reducer
from langgraph.prebuilt import ToolNode


class Configuration(TypedDict):
    """Configurable parameters for the SQL agent.

    Set these when creating assistants OR when invoking the graph.
    """

    database_uri: str
    model_name: str
    api_base: str
    api_key: str


class State(TypedDict):
    """State for the SQL agent."""

    messages: Annotated[Sequence[BaseMessage], add_messages]
    ui: Annotated[Sequence[AnyUIMessage], ui_message_reducer]
    database_uri: str
    current_query: str
    query_result: Any
    error_message: str


def initialize_database(database_uri: str) -> SQLDatabase:
    """Initialize database connection."""
    if database_uri.startswith("sqlite:///") and not os.path.exists(database_uri.replace("sqlite:///", "")):
        # Download sample Chinook database if it doesn't exist
        url = "https://storage.googleapis.com/benchmarks-artifacts/chinook/Chinook.db"
        db_file = database_uri.replace("sqlite:///", "")

        try:
            response = requests.get(url)
            if response.status_code == 200:
                with open(db_file, "wb") as file:
                    file.write(response.content)
        except Exception as e:
            print(f"Failed to download sample database: {e}")

    return SQLDatabase.from_uri(database_uri)


def initialize_llm(config: RunnableConfig) -> ChatOpenAI:
    """Initialize LLM with DeepSeek compatibility."""
    configuration = config.get("configurable", {})

    # DeepSeek API configuration
    api_base = configuration.get("api_base", "https://api.deepseek.com/v1")
    api_key = configuration.get("api_key", os.getenv("DEEPSEEK_API_KEY"))
    model_name = configuration.get("model_name", "deepseek-chat")

    if api_key:
        # Use DeepSeek API
        return ChatOpenAI(
            model=model_name,
            api_key=api_key,
            base_url=api_base,
        )
    else:
        # Fallback to OpenAI
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key:
            return ChatOpenAI(
                model="gpt-4o-mini",
                api_key=openai_key
            )
        else:
            # Use default configuration
            return ChatOpenAI(model="gpt-4o-mini")


async def list_tables(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """List available database tables."""
    try:
        database_uri = state.get("database_uri", "sqlite:///Chinook.db")
        db = initialize_database(database_uri)

        tables = db.get_usable_table_names()

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=f"Available tables: {', '.join(tables)}"
        )

        # Push UI message to display tables
        push_ui_message(
            "table_list",
            {"tables": tables, "database": database_uri},
            message=message
        )

        return {
            "messages": [message],
            "database_uri": database_uri
        }

    except Exception as e:
        error_msg = f"Error listing tables: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {
            "messages": [message],
            "error_message": error_msg
        }


async def get_schema(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Get schema for relevant tables."""
    try:
        database_uri = state.get("database_uri", "sqlite:///Chinook.db")
        db = initialize_database(database_uri)
        llm = initialize_llm(config)

        # Get the last user message to understand what tables are needed
        user_messages = [msg for msg in state["messages"] if msg.type == "human"]
        if not user_messages:
            return {"messages": []}

        last_question = user_messages[-1].content

        # Use LLM to determine relevant tables
        toolkit = SQLDatabaseToolkit(db=db, llm=llm)
        tools = toolkit.get_tools()

        # Get schema tool
        schema_tool = next(tool for tool in tools if tool.name == "sql_db_schema")

        # For simplicity, get schema for common tables that might be relevant
        # In a real implementation, you'd use the LLM to determine this
        common_tables = ["Track", "Genre", "Artist", "Album", "Customer"]
        available_tables = db.get_usable_table_names()
        relevant_tables = [t for t in common_tables if t in available_tables]

        if relevant_tables:
            schema_result = schema_tool.invoke(",".join(relevant_tables))

            message = AIMessage(
                id=str(uuid.uuid4()),
                content=f"Retrieved schema for tables: {', '.join(relevant_tables)}"
            )

            # Push UI message to display schema
            push_ui_message(
                "schema_display",
                {
                    "tables": relevant_tables,
                    "schema": schema_result.content,
                    "question": last_question
                },
                message=message
            )

            return {"messages": [message]}
        else:
            message = AIMessage(
                id=str(uuid.uuid4()),
                content="No relevant tables found for the query."
            )
            return {"messages": [message]}

    except Exception as e:
        error_msg = f"Error getting schema: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {
            "messages": [message],
            "error_message": error_msg
        }


async def generate_query(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Generate SQL query based on the question."""
    try:
        database_uri = state.get("database_uri", "sqlite:///Chinook.db")
        db = initialize_database(database_uri)
        llm = initialize_llm(config)

        # Get the user question
        user_messages = [msg for msg in state["messages"] if msg.type == "human"]
        if not user_messages:
            return {"messages": []}

        question = user_messages[-1].content

        # Generate query using LLM
        system_prompt = f"""
        You are an expert SQL query generator. Given a question about a {db.dialect} database,
        create a syntactically correct SQL query.

        Always limit your query to at most 10 results unless specified otherwise.
        Never query for all columns from a table, only ask for relevant columns.
        DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.).

        Available tables: {', '.join(db.get_usable_table_names())}

        Question: {question}

        Return only the SQL query, nothing else.
        """

        query_response = await llm.ainvoke([{"role": "system", "content": system_prompt}])
        sql_query = query_response.content.strip()

        # Clean up the query (remove markdown formatting if present)
        if sql_query.startswith("```sql"):
            sql_query = sql_query.replace("```sql", "").replace("```", "").strip()
        elif sql_query.startswith("```"):
            sql_query = sql_query.replace("```", "").strip()

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=f"Generated SQL query: {sql_query}"
        )

        # Push UI message to display the generated query
        push_ui_message(
            "query_display",
            {
                "query": sql_query,
                "question": question,
                "status": "generated"
            },
            message=message
        )

        return {
            "messages": [message],
            "current_query": sql_query
        }

    except Exception as e:
        error_msg = f"Error generating query: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {
            "messages": [message],
            "error_message": error_msg
        }


async def execute_query(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Execute the SQL query and return results."""
    try:
        database_uri = state.get("database_uri", "sqlite:///Chinook.db")
        db = initialize_database(database_uri)

        query = state.get("current_query", "")
        if not query:
            message = AIMessage(
                id=str(uuid.uuid4()),
                content="No query to execute."
            )
            return {"messages": [message]}

        # Execute the query
        result = db.run(query)

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=f"Query executed successfully. Found {len(result) if isinstance(result, list) else 'some'} results."
        )

        # Push UI message to display results
        push_ui_message(
            "query_results",
            {
                "query": query,
                "results": result,
                "status": "success",
                "count": len(result) if isinstance(result, list) else 0
            },
            message=message
        )

        return {
            "messages": [message],
            "query_result": result
        }

    except Exception as e:
        error_msg = f"Error executing query: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)

        # Push UI message to display error
        push_ui_message(
            "query_error",
            {
                "query": state.get("current_query", ""),
                "error": error_msg,
                "status": "error"
            },
            message=message
        )

        return {
            "messages": [message],
            "error_message": error_msg
        }


async def format_response(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Format the final response based on query results."""
    try:
        llm = initialize_llm(config)

        # Get the original question
        user_messages = [msg for msg in state["messages"] if msg.type == "human"]
        question = user_messages[-1].content if user_messages else "Unknown question"

        query = state.get("current_query", "")
        result = state.get("query_result", [])

        if state.get("error_message"):
            response_content = f"I encountered an error while processing your question: {state['error_message']}"
        else:
            # Use LLM to format a natural language response
            format_prompt = f"""
            Based on the SQL query results, provide a clear and helpful answer to the user's question.

            Original question: {question}
            SQL query used: {query}
            Query results: {result}

            Provide a natural language answer that directly addresses the user's question.
            """

            response = await llm.ainvoke([{"role": "system", "content": format_prompt}])
            response_content = response.content

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=response_content
        )

        # Push final UI message with formatted response
        push_ui_message(
            "final_response",
            {
                "question": question,
                "answer": response_content,
                "query": query,
                "results": result
            },
            message=message
        )

        return {"messages": [message]}

    except Exception as e:
        error_msg = f"Error formatting response: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {"messages": [message]}


def should_continue(state: State) -> Literal["execute_query", "format_response"]:
    """Determine next step based on current state."""
    if state.get("error_message"):
        return "format_response"
    elif state.get("current_query") and not state.get("query_result"):
        return "execute_query"
    else:
        return "format_response"


# Define the graph
builder = StateGraph(State, config_schema=Configuration)

# Add nodes
builder.add_node("list_tables", list_tables)
builder.add_node("get_schema", get_schema)
builder.add_node("generate_query", generate_query)
builder.add_node("execute_query", execute_query)
builder.add_node("format_response", format_response)

# Add edges
builder.add_edge(START, "list_tables")
builder.add_edge("list_tables", "get_schema")
builder.add_edge("get_schema", "generate_query")
builder.add_conditional_edges(
    "generate_query",
    should_continue,
    {
        "execute_query": "execute_query",
        "format_response": "format_response"
    }
)
builder.add_edge("execute_query", "format_response")
builder.add_edge("format_response", END)

# Compile the graph
graph = builder.compile(name="SQL Agent with UI")
