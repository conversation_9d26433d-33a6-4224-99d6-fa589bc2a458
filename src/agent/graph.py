"""SQL Agent with UI support using LangGraph.

This agent can answer questions about SQL databases and generate UI components
to display results. Compatible with DeepSeek API.
"""

from __future__ import annotations

import os
import uuid
from typing import Annotated, Any, Dict, Literal, Sequence, TypedDict

import requests
from langchain_openai import ChatOpenAI
from langchain_community.agent_toolkits import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.graph.ui import AnyUIMessage, push_ui_message, ui_message_reducer
from langgraph.prebuilt import ToolNode


class Configuration(TypedDict):
    """Configurable parameters for the SQL agent.

    Set these when creating assistants OR when invoking the graph.
    """

    database_url: str
    llm_provider: str
    llm_api_key: str
    llm_base_url: str
    llm_model: str
    llm_temperature: float
    llm_max_tokens: int


class State(TypedDict):
    """State for the SQL agent."""

    messages: Annotated[Sequence[BaseMessage], add_messages]
    ui: Annotated[Sequence[AnyUIMessage], ui_message_reducer]
    database_url: str
    current_query: str
    query_result: Any
    error_message: str


def initialize_database(database_url: str) -> SQLDatabase:
    """Initialize database connection."""
    try:
        # For PostgreSQL connections, use the provided URL directly
        return SQLDatabase.from_uri(database_url)
    except Exception as e:
        print(f"Failed to connect to database: {e}")
        # Fallback to sample SQLite database for testing
        fallback_db = "sqlite:///Chinook.db"
        if not os.path.exists("Chinook.db"):
            try:
                url = "https://storage.googleapis.com/benchmarks-artifacts/chinook/Chinook.db"
                response = requests.get(url)
                if response.status_code == 200:
                    with open("Chinook.db", "wb") as file:
                        file.write(response.content)
            except Exception as download_error:
                print(f"Failed to download sample database: {download_error}")

        return SQLDatabase.from_uri(fallback_db)


def initialize_llm(config: RunnableConfig) -> ChatOpenAI:
    """Initialize LLM with DeepSeek configuration."""
    configuration = config.get("configurable", {})

    # Get configuration from environment or config
    llm_provider = configuration.get("llm_provider", os.getenv("LLM_PROVIDER", "deepseek"))
    llm_api_key = configuration.get("llm_api_key", os.getenv("LLM_API_KEY"))
    llm_base_url = configuration.get("llm_base_url", os.getenv("LLM_BASE_URL", "https://api.deepseek.com"))
    llm_model = configuration.get("llm_model", os.getenv("LLM_MODEL", "deepseek-chat"))
    llm_temperature = float(configuration.get("llm_temperature", os.getenv("LLM_TEMPERATURE", "0.1")))
    llm_max_tokens = int(configuration.get("llm_max_tokens", os.getenv("LLM_MAX_TOKENS", "8000")))

    if llm_provider == "deepseek" and llm_api_key:
        return ChatOpenAI(
            model=llm_model,
            api_key=llm_api_key,
            base_url=llm_base_url,
            temperature=llm_temperature,
            max_tokens=llm_max_tokens,
        )
    else:
        # Fallback configuration
        return ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.1,
            max_tokens=8000
        )


async def list_tables(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """List available database tables."""
    try:
        configuration = config.get("configurable", {})
        database_url = configuration.get("database_url", os.getenv("DATABASE_URL", "postgresql://zhaoyingnan:zhaoyingnan%40119@192.168.20.70:18640/abument_firefighting_supervision"))

        db = initialize_database(database_url)
        tables = db.get_usable_table_names()

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=f"Connected to database. Available tables: {', '.join(tables[:10])}{'...' if len(tables) > 10 else ''} (Total: {len(tables)} tables)"
        )

        # Push UI message to display tables
        push_ui_message(
            "table_list",
            {"tables": tables, "database": database_url, "total_count": len(tables)},
            message=message
        )

        return {
            "messages": [message],
            "database_url": database_url
        }

    except Exception as e:
        error_msg = f"Error listing tables: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {
            "messages": [message],
            "error_message": error_msg
        }


async def get_schema(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Get schema for relevant tables."""
    try:
        database_url = state.get("database_url", os.getenv("DATABASE_URL"))
        db = initialize_database(database_url)
        llm = initialize_llm(config)

        # Get the last user message to understand what tables are needed
        user_messages = [msg for msg in state["messages"] if msg.type == "human"]
        if not user_messages:
            return {"messages": []}

        last_question = user_messages[-1].content

        # Use LLM to determine relevant tables
        toolkit = SQLDatabaseToolkit(db=db, llm=llm)
        tools = toolkit.get_tools()

        # Get schema tool
        schema_tool = next(tool for tool in tools if tool.name == "sql_db_schema")

        # Get all available tables and select first few for schema analysis
        available_tables = db.get_usable_table_names()

        # For PostgreSQL, limit to first 5 tables to avoid overwhelming the context
        relevant_tables = available_tables[:5] if available_tables else []

        if relevant_tables:
            schema_result = schema_tool.invoke(",".join(relevant_tables))

            message = AIMessage(
                id=str(uuid.uuid4()),
                content=f"Retrieved schema for tables: {', '.join(relevant_tables)}"
            )

            # Push UI message to display schema
            push_ui_message(
                "schema_display",
                {
                    "tables": relevant_tables,
                    "schema": schema_result.content,
                    "question": last_question,
                    "total_tables": len(available_tables)
                },
                message=message
            )

            return {"messages": [message]}
        else:
            message = AIMessage(
                id=str(uuid.uuid4()),
                content="No tables found in the database."
            )
            return {"messages": [message]}

    except Exception as e:
        error_msg = f"Error getting schema: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {
            "messages": [message],
            "error_message": error_msg
        }


async def generate_query(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Generate SQL query based on the question."""
    try:
        database_url = state.get("database_url", os.getenv("DATABASE_URL"))
        db = initialize_database(database_url)
        llm = initialize_llm(config)

        # Get the user question
        user_messages = [msg for msg in state["messages"] if msg.type == "human"]
        if not user_messages:
            return {"messages": []}

        question = user_messages[-1].content

        # Get available tables (limit to avoid context overflow)
        available_tables = db.get_usable_table_names()
        table_list = ', '.join(available_tables[:20])  # Limit to first 20 tables

        # Generate query using LLM with PostgreSQL-specific instructions
        system_prompt = f"""
        You are an expert PostgreSQL query generator. Given a question about a PostgreSQL database,
        create a syntactically correct SQL query.

        Database: abument_firefighting_supervision (消防监管数据库)

        Guidelines:
        - Always limit your query to at most 10 results unless specified otherwise
        - Use proper PostgreSQL syntax and functions
        - Never query for all columns from a table, only ask for relevant columns
        - DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.)
        - Use LIMIT clause for result limiting
        - Consider using proper JOIN syntax when needed
        - Be aware this is a Chinese firefighting supervision database

        Available tables (first 20): {table_list}
        Total tables available: {len(available_tables)}

        Question: {question}

        Return only the SQL query, nothing else. Do not include markdown formatting.
        """

        query_response = await llm.ainvoke([{"role": "system", "content": system_prompt}])
        sql_query = query_response.content.strip()

        # Clean up the query (remove markdown formatting if present)
        if sql_query.startswith("```sql"):
            sql_query = sql_query.replace("```sql", "").replace("```", "").strip()
        elif sql_query.startswith("```"):
            sql_query = sql_query.replace("```", "").strip()

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=f"Generated PostgreSQL query: {sql_query}"
        )

        # Push UI message to display the generated query
        push_ui_message(
            "query_display",
            {
                "query": sql_query,
                "question": question,
                "status": "generated",
                "database_type": "PostgreSQL"
            },
            message=message
        )

        return {
            "messages": [message],
            "current_query": sql_query
        }

    except Exception as e:
        error_msg = f"Error generating query: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {
            "messages": [message],
            "error_message": error_msg
        }


async def execute_query(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Execute the SQL query and return results."""
    try:
        database_url = state.get("database_url", os.getenv("DATABASE_URL"))
        db = initialize_database(database_url)

        query = state.get("current_query", "")
        if not query:
            message = AIMessage(
                id=str(uuid.uuid4()),
                content="No query to execute."
            )
            return {"messages": [message]}

        # Execute the query with timeout protection
        result = db.run(query)

        # Handle different result types
        result_count = 0
        if isinstance(result, list):
            result_count = len(result)
        elif isinstance(result, str) and result.strip():
            result_count = 1

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=f"Query executed successfully on PostgreSQL database. Found {result_count} result{'s' if result_count != 1 else ''}."
        )

        # Push UI message to display results
        push_ui_message(
            "query_results",
            {
                "query": query,
                "results": result,
                "status": "success",
                "count": result_count,
                "database_type": "PostgreSQL"
            },
            message=message
        )

        return {
            "messages": [message],
            "query_result": result
        }

    except Exception as e:
        error_msg = f"Error executing PostgreSQL query: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)

        # Push UI message to display error
        push_ui_message(
            "query_error",
            {
                "query": state.get("current_query", ""),
                "error": error_msg,
                "status": "error",
                "database_type": "PostgreSQL"
            },
            message=message
        )

        return {
            "messages": [message],
            "error_message": error_msg
        }


async def format_response(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Format the final response based on query results."""
    try:
        llm = initialize_llm(config)

        # Get the original question
        user_messages = [msg for msg in state["messages"] if msg.type == "human"]
        question = user_messages[-1].content if user_messages else "Unknown question"

        query = state.get("current_query", "")
        result = state.get("query_result", [])

        if state.get("error_message"):
            response_content = f"抱歉，在处理您的问题时遇到了错误：{state['error_message']}\n\n请检查您的问题是否清晰，或者尝试用不同的方式表达。"
        else:
            # Use LLM to format a natural language response in Chinese
            format_prompt = f"""
            基于PostgreSQL查询结果，为用户的问题提供清晰有用的中文回答。

            用户问题: {question}
            使用的SQL查询: {query}
            查询结果: {result}

            请用中文提供直接回答用户问题的自然语言答案。如果结果为空，请说明没有找到相关数据。
            如果是消防监管相关的数据，请结合业务背景进行解释。
            """

            response = await llm.ainvoke([{"role": "system", "content": format_prompt}])
            response_content = response.content

        message = AIMessage(
            id=str(uuid.uuid4()),
            content=response_content
        )

        # Push final UI message with formatted response
        push_ui_message(
            "final_response",
            {
                "question": question,
                "answer": response_content,
                "query": query,
                "results": result,
                "database_type": "PostgreSQL",
                "database_name": "abument_firefighting_supervision"
            },
            message=message
        )

        return {"messages": [message]}

    except Exception as e:
        error_msg = f"Error formatting response: {str(e)}"
        message = AIMessage(id=str(uuid.uuid4()), content=error_msg)
        return {"messages": [message]}


def should_continue(state: State) -> Literal["execute_query", "format_response"]:
    """Determine next step based on current state."""
    if state.get("error_message"):
        return "format_response"
    elif state.get("current_query") and not state.get("query_result"):
        return "execute_query"
    else:
        return "format_response"


# Define the graph
builder = StateGraph(State, config_schema=Configuration)

# Add nodes
builder.add_node("list_tables", list_tables)
builder.add_node("get_schema", get_schema)
builder.add_node("generate_query", generate_query)
builder.add_node("execute_query", execute_query)
builder.add_node("format_response", format_response)

# Add edges
builder.add_edge(START, "list_tables")
builder.add_edge("list_tables", "get_schema")
builder.add_edge("get_schema", "generate_query")
builder.add_conditional_edges(
    "generate_query",
    should_continue,
    {
        "execute_query": "execute_query",
        "format_response": "format_response"
    }
)
builder.add_edge("execute_query", "format_response")
builder.add_edge("format_response", END)

# Compile the graph
graph = builder.compile(name="SQL Agent with UI")
